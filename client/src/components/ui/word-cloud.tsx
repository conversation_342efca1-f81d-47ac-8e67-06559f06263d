import React from 'react';
import WordCloud from 'react-d3-cloud';

interface WordData {
  text: string;
  value: number;
}

interface WordCloudComponentProps {
  data: WordData[];
  width?: number;
  height?: number;
  fontSizeMapper?: (word: WordData) => number;
  rotate?: (word: WordData) => number;
  className?: string;
}

export const WordCloudComponent: React.FC<WordCloudComponentProps> = ({
  data,
  width = 400,
  height = 200,
  fontSizeMapper = (word: WordData) => Math.log2(word.value) * 5 + 10,
  rotate = () => (Math.random() - 0.5) * 60,
  className = "",
}) => {
  const fillColor = (word: WordData) => {
    const colors = ['#7c47e6', '#4f9ef0', '#2bb684', '#f84242', '#a37eff', '#adb0b4'];
    return colors[word.value % colors.length];
  };

  return (
    <div className={`word-cloud-container ${className}`} data-testid="word-cloud">
      <WordCloud
        data={data}
        width={width}
        height={height}
        font="Inter"
        fontStyle="normal"
        fontWeight="600"
        fontSize={fontSizeMapper}
        spiral="rectangular"
        rotate={rotate}
        padding={2}
        random={() => 0.5}
        fill={fillColor}
      />
    </div>
  );
};
